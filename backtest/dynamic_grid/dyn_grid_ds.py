import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import logging
import uuid
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Data source imports
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    print("yfinance not available. Install with: pip install yfinance")

try:
    import ccxt
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
    print("ccxt not available. Install with: pip install ccxt")

try:
    import pandas_datareader as pdr
    DATAREADER_AVAILABLE = True
except ImportError:
    DATAREADER_AVAILABLE = False
    print("pandas-datareader not available. Install with: pip install pandas-datareader")

# ==============================================================================
# DATA SOURCE MANAGERS
# ==============================================================================

class DataSource(Enum):
    YAHOO = "yahoo"
    BINANCE = "binance"
    COINBASE = "coinbase"
    CSV_FILE = "csv"
    PANDAS_DATAREADER = "pandas_datareader"
    REAL_TIME = "real_time"

@dataclass
class MarketData:
    timestamp: pd.Timestamp
    open: float
    high: float
    low: float
    close: float
    volume: float
    symbol: str

class RealDataManager:
    def __init__(self):
        self.data_cache = {}
        
    def fetch_yahoo_data(self, symbol: str, period: str = "1y", 
                        interval: str = "1h") -> pd.DataFrame:
        """Fetch data from Yahoo Finance"""
        if not YFINANCE_AVAILABLE:
            raise ImportError("yfinance is required for Yahoo data")
        
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                raise ValueError(f"No data found for symbol {symbol}")
            
            # Standardize column names
            data.columns = [col.lower() for col in data.columns]
            data['symbol'] = symbol
            
            return data
        except Exception as e:
            raise Exception(f"Error fetching Yahoo data for {symbol}: {str(e)}")
    
    def fetch_crypto_data(self, symbol: str, exchange: str = "binance", 
                         timeframe: str = "1h", limit: int = 1000) -> pd.DataFrame:
        """Fetch cryptocurrency data using ccxt"""
        if not CCXT_AVAILABLE:
            raise ImportError("ccxt is required for crypto data")
        
        try:
            # Initialize exchange
            exchange_class = getattr(ccxt, exchange)
            exchange_obj = exchange_class({'enableRateLimit': True})
            
            # Fetch OHLCV data
            ohlcv = exchange_obj.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            df['symbol'] = symbol
            
            return df
            
        except Exception as e:
            raise Exception(f"Error fetching crypto data for {symbol}: {str(e)}")
    
    def load_csv_data(self, file_path: str, timestamp_col: str = 'timestamp',
                     price_col: str = 'close') -> pd.DataFrame:
        """Load data from CSV file"""
        try:
            df = pd.read_csv(file_path)
            
            # Convert timestamp column
            df[timestamp_col] = pd.to_datetime(df[timestamp_col])
            df.set_index(timestamp_col, inplace=True)
            
            # Ensure required columns exist
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            missing_cols = []
            
            for col in required_cols:
                if col not in df.columns:
                    if col == 'open' and price_col in df.columns:
                        df['open'] = df[price_col]
                    elif col == 'high' and price_col in df.columns:
                        df['high'] = df[price_col]
                    elif col == 'low' and price_col in df.columns:
                        df['low'] = df[price_col]
                    elif col == 'close' and price_col in df.columns:
                        df['close'] = df[price_col]
                    elif col == 'volume':
                        df['volume'] = 0  # Default volume if not available
                    else:
                        missing_cols.append(col)
            
            if missing_cols:
                print(f"Warning: Missing columns {missing_cols}, using defaults")
            
            return df
            
        except Exception as e:
            raise Exception(f"Error loading CSV data: {str(e)}")
    
    def fetch_data(self, source: DataSource, symbol: str, **kwargs) -> pd.DataFrame:
        """Universal data fetching method"""
        cache_key = f"{source.value}_{symbol}_{str(kwargs)}"
        
        if cache_key in self.data_cache:
            return self.data_cache[cache_key]
        
        if source == DataSource.YAHOO:
            data = self.fetch_yahoo_data(symbol, **kwargs)
        elif source == DataSource.BINANCE:
            data = self.fetch_crypto_data(symbol, exchange="binance", **kwargs)
        elif source == DataSource.COINBASE:
            data = self.fetch_crypto_data(symbol, exchange="coinbasepro", **kwargs)
        elif source == DataSource.CSV_FILE:
            data = self.load_csv_data(kwargs.get('file_path'), **kwargs)
        else:
            raise ValueError(f"Unsupported data source: {source}")
        
        self.data_cache[cache_key] = data
        return data

class RealTimeDataFeed:
    """Real-time data feed simulator/handler"""
    def __init__(self, data_manager: RealDataManager, source: DataSource, 
                 symbol: str, **source_kwargs):
        self.data_manager = data_manager
        self.source = source
        self.symbol = symbol
        self.source_kwargs = source_kwargs
        self.historical_data = None
        self.current_index = 0
        self.callback_functions = []
        
    def load_historical_data(self):
        """Load historical data for backtesting"""
        self.historical_data = self.data_manager.fetch_data(
            self.source, self.symbol, **self.source_kwargs
        )
        self.current_index = 0
        
    def add_callback(self, callback_func):
        """Add callback function to receive new data"""
        self.callback_functions.append(callback_func)
        
    def get_next_tick(self) -> Optional[MarketData]:
        """Get next data point (for backtesting)"""
        if self.historical_data is None:
            self.load_historical_data()
            
        if self.current_index >= len(self.historical_data):
            return None
            
        row = self.historical_data.iloc[self.current_index]
        market_data = MarketData(
            timestamp=row.name,
            open=row['open'],
            high=row['high'],
            low=row['low'],
            close=row['close'],
            volume=row['volume'],
            symbol=self.symbol
        )
        
        self.current_index += 1
        
        # Call callbacks
        for callback in self.callback_functions:
            callback(market_data)
            
        return market_data
    
    def start_real_time_feed(self, update_interval: int = 60):
        """Start real-time data feed (simplified implementation)"""
        import time
        
        while True:
            try:
                # Fetch latest data
                latest_data = self.data_manager.fetch_data(
                    self.source, self.symbol, period="1d", interval="1m"
                )
                
                if not latest_data.empty:
                    latest_row = latest_data.iloc[-1]
                    market_data = MarketData(
                        timestamp=latest_row.name,
                        open=latest_row['open'],
                        high=latest_row['high'],
                        low=latest_row['low'],
                        close=latest_row['close'],
                        volume=latest_row['volume'],
                        symbol=self.symbol
                    )
                    
                    # Call callbacks
                    for callback in self.callback_functions:
                        callback(market_data)
                
                time.sleep(update_interval)
                
            except Exception as e:
                print(f"Error in real-time feed: {e}")
                time.sleep(update_interval)

# ==============================================================================
# UPDATED GRID SYSTEM WITH REAL DATA INTEGRATION
# ==============================================================================

# [Previous grid system classes remain the same - OrderType, OrderStatus, GridOrder, Position, etc.]
# [Including all the manager classes from the previous implementation]
# [I'll include the key modifications needed for real data integration]

class OrderType(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"

@dataclass
class GridOrder:
    id: str
    price: float
    quantity: float
    order_type: OrderType
    status: OrderStatus
    timestamp: pd.Timestamp
    grid_level: int
    hedge_pair_id: Optional[str] = None
    is_position_management: bool = False
    grid_side: str = "long"  # "long" or "short" to track which grid this order belongs to
    action_label: str = ""  # "open_long", "close_long", "open_short", "close_short"

@dataclass
class Position:
    symbol: str
    quantity: float = 0.0
    avg_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0

@dataclass
class HedgedPosition:
    """Hedged position with separate long and short positions"""
    symbol: str
    long_position: Position = None
    short_position: Position = None

    def __post_init__(self):
        if self.long_position is None:
            self.long_position = Position(self.symbol)
        if self.short_position is None:
            self.short_position = Position(self.symbol)

    @property
    def net_quantity(self) -> float:
        """Net position (long - short)"""
        return self.long_position.quantity - self.short_position.quantity

    @property
    def total_unrealized_pnl(self) -> float:
        """Total unrealized PnL from both positions"""
        return self.long_position.unrealized_pnl + self.short_position.unrealized_pnl

    @property
    def total_realized_pnl(self) -> float:
        """Total realized PnL from both positions"""
        return self.long_position.realized_pnl + self.short_position.realized_pnl

# [Include all previous manager classes here - AdaptiveRangeManager, MarketRegimeManager, etc.]
# [For brevity, I'm showing the key integration parts]

class RealDataDynamicHedgedGrid:
    """Enhanced grid system with real data integration"""
    
    def __init__(
        self,
        symbol: str,
        data_source: DataSource,
        initial_capital: float = 10000,
        grid_density: int = 20,
        volatility_lookback: int = 100,
        range_lookback: int = 500,
        position_size_pct: float = 0.02,
        max_position_ratio: float = 0.3,
        rebalance_threshold: float = 0.1,
        stop_loss_pct: float = 0.15,
        **data_source_kwargs
    ):
        # Core parameters
        self.symbol = symbol
        self.data_source = data_source
        self.data_source_kwargs = data_source_kwargs
        self.initial_capital = initial_capital
        self.available_capital = initial_capital
        self.grid_density = grid_density
        self.volatility_lookback = volatility_lookback
        self.range_lookback = range_lookback
        self.position_size_pct = position_size_pct
        self.max_position_ratio = max_position_ratio
        self.rebalance_threshold = rebalance_threshold
        self.stop_loss_pct = stop_loss_pct
        
        # Data management
        self.data_manager = RealDataManager()
        self.data_feed = RealTimeDataFeed(
            self.data_manager, data_source, symbol, **data_source_kwargs
        )
        
        # Market data storage
        self.market_data_history: List[MarketData] = []
        self.price_history: List[float] = []
        self.ohlcv_data: pd.DataFrame = pd.DataFrame()
        
        # Grid state
        self.current_range: Tuple[float, float] = (0, 0)
        self.grid_levels: List[float] = []
        self.num_grid_levels = grid_density  # Number of grid levels
        self.grid_size_usd = initial_capital * position_size_pct  # Dollar amount per grid order
        self.active_orders: Dict[str, GridOrder] = {}
        self.filled_orders: List[GridOrder] = []
        self.position = HedgedPosition(symbol)  # Use hedged position instead
        
        # Hedging state
        self.long_grid_orders: Dict[int, GridOrder] = {}
        self.short_grid_orders: Dict[int, GridOrder] = {}
        self.hedge_pairs: Dict[str, str] = {}
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_fees = 0
        self.equity_curve: List[float] = []
        self.timestamps: List[pd.Timestamp] = []
        self.last_rebalance_time = None  # Track last rebalance time

        # Debug tracking
        self.kline_counter = 0
        self.verbose_debug = True  # Set to True for detailed k-line logging
        
        # Initialize data feed callback
        self.data_feed.add_callback(self.on_new_market_data)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def on_new_market_data(self, market_data: MarketData):
        """Callback function for new market data"""
        self.market_data_history.append(market_data)
        self.price_history.append(market_data.close)
        self.timestamps.append(market_data.timestamp)

        # Update OHLCV DataFrame
        new_row = pd.DataFrame({
            'open': [market_data.open],
            'high': [market_data.high],
            'low': [market_data.low],
            'close': [market_data.close],
            'volume': [market_data.volume]
        }, index=[market_data.timestamp])

        if self.ohlcv_data.empty:
            self.ohlcv_data = new_row
        else:
            self.ohlcv_data = pd.concat([self.ohlcv_data, new_row])

        # Keep only recent data
        if len(self.ohlcv_data) > self.range_lookback * 2:
            self.ohlcv_data = self.ohlcv_data.tail(self.range_lookback * 2)
            self.price_history = self.price_history[-self.range_lookback * 2:]
            self.market_data_history = self.market_data_history[-self.range_lookback * 2:]

        # Update unrealized PnL and equity curve for every data point
        current_price = market_data.close

        # Update unrealized PnL for both long and short positions
        if self.position.long_position.quantity != 0:
            self.position.long_position.unrealized_pnl = (current_price - self.position.long_position.avg_price) * self.position.long_position.quantity

        if self.position.short_position.quantity != 0:
            # For short positions, PnL is inverted (profit when price goes down)
            self.position.short_position.unrealized_pnl = (self.position.short_position.avg_price - current_price) * self.position.short_position.quantity

        # Update equity curve - this must happen for every data point to match timestamps
        # Total equity = available cash + current market value of both positions
        long_position_value = self.position.long_position.quantity * current_price
        short_position_value = -self.position.short_position.quantity * current_price  # Short position has negative value
        total_equity = self.available_capital + long_position_value + short_position_value
        self.equity_curve.append(total_equity)

        # Update grid system
        self.update_with_market_data(market_data)

        # Print detailed state for every k-line
        self.print_kline_state(market_data)
    
    def update_with_market_data(self, market_data: MarketData):
        """Update grid system with new market data"""
        current_price = market_data.close

        # Check stop loss
        if self.check_stop_loss(current_price):
            self.logger.warning("Stop loss triggered, closing all positions")
            self.cancel_all_orders()
            return

        # Update range and rebalance if needed
        if len(self.price_history) >= 20:
            new_range = self.calculate_dynamic_range(self.price_history)

            if self.should_rebalance_grid(current_price, market_data.timestamp):
                self.update_range_with_position_management(new_range, current_price, "rebalance_trigger")
                self.last_rebalance_time = market_data.timestamp  # Track rebalance time
                self.logger.info(f"Grid rebalanced. Range: {self.current_range[0]:.2f} - {self.current_range[1]:.2f}")
                return

        # Execute pending orders
        orders_to_execute = list(self.active_orders.values())
        for order in orders_to_execute:
            self.execute_order(order, current_price, market_data.timestamp)

        # Debug: Log first few data points to see what's happening
        if len(self.price_history) <= 5:
            self.logger.info(f"Price: {current_price:.2f}, Active orders: {len(self.active_orders)}")
            for order_id, order in list(self.active_orders.items())[:3]:  # Show first 3 orders
                self.logger.info(f"  Order {order_id}: {order.order_type.value} @ {order.price:.2f}")
    
    def load_and_backtest(self, start_date: str = None, end_date: str = None):
        """Load historical data and run backtest"""
        try:
            # Load historical data
            if start_date and end_date:
                if self.data_source == DataSource.YAHOO:
                    self.data_source_kwargs.update({
                        'start': start_date,
                        'end': end_date
                    })
                    
            self.data_feed.load_historical_data()
            
            if self.data_feed.historical_data.empty:
                raise ValueError("No historical data loaded")
            
            print(f"Loaded {len(self.data_feed.historical_data)} data points")
            print(f"Date range: {self.data_feed.historical_data.index[0]} to {self.data_feed.historical_data.index[-1]}")
            
            # Run backtest
            total_ticks = len(self.data_feed.historical_data)
            print_interval = max(1, total_ticks // 20)  # Print 20 progress updates
            
            while True:
                market_data = self.data_feed.get_next_tick()
                if market_data is None:
                    break
                    
                # Progress update
                if self.data_feed.current_index % print_interval == 0:
                    progress = (self.data_feed.current_index / total_ticks) * 100
                    status = self.get_status()
                    print(f"Progress: {progress:5.1f}% | "
                          f"Price: ${market_data.close:8.2f} | "
                          f"Long: {status['position']['long_quantity']:6.4f} | "
                          f"Short: {status['position']['short_quantity']:6.4f} | "
                          f"Net: {status['position']['net_quantity']:6.4f} | "
                          f"Equity: ${status['total_equity']:10.2f} | "
                          f"Orders: {status['active_orders']:2d}")
            
            print("Backtest completed!")
            return self.get_performance_metrics()
            
        except Exception as e:
            self.logger.error(f"Error in backtest: {e}")
            raise
    
    def start_live_trading(self, update_interval: int = 60):
        """Start live trading with real-time data"""
        print(f"Starting live trading for {self.symbol}")
        print("WARNING: This is a demo implementation. Do not use with real money without proper testing!")
        
        try:
            self.data_feed.start_real_time_feed(update_interval)
        except KeyboardInterrupt:
            print("Live trading stopped by user")
        except Exception as e:
            self.logger.error(f"Error in live trading: {e}")
    
    # [Include all the methods from the previous DynamicHedgedGrid class]
    # [calculate_dynamic_range, calculate_atr, generate_grid_levels, etc.]
    
    def calculate_dynamic_range(self, prices: List[float]) -> Tuple[float, float]:
        """Calculate trading range using multiple methods"""
        if len(prices) < self.range_lookback:
            return (min(prices), max(prices))
        
        recent_prices = prices[-self.range_lookback:]
        
        # Method 1: Bollinger Bands approach
        mean_price = np.mean(recent_prices)
        std_price = np.std(recent_prices)
        bb_upper = mean_price + 2 * std_price
        bb_lower = mean_price - 2 * std_price
        
        # Method 2: Percentile approach
        percentile_upper = np.percentile(recent_prices, 95)
        percentile_lower = np.percentile(recent_prices, 5)
        
        # Method 3: ATR-based approach using OHLC data if available
        if not self.ohlcv_data.empty and len(self.ohlcv_data) >= 14:
            atr = self.calculate_atr_from_ohlc(self.ohlcv_data.tail(50))
            current_price = prices[-1]
            atr_upper = current_price + 3 * atr
            atr_lower = current_price - 3 * atr
        else:
            atr_upper = bb_upper
            atr_lower = bb_lower
        
        # Combine methods with weights
        upper_bound = 0.4 * bb_upper + 0.4 * percentile_upper + 0.2 * atr_upper
        lower_bound = 0.4 * bb_lower + 0.4 * percentile_lower + 0.2 * atr_lower
        
        # Ensure minimum range
        range_size = upper_bound - lower_bound
        min_range = mean_price * 0.05  # Minimum 5% range
        
        if range_size < min_range:
            mid_point = (upper_bound + lower_bound) / 2
            upper_bound = mid_point + min_range / 2
            lower_bound = mid_point - min_range / 2
        
        return (lower_bound, upper_bound)
    
    def calculate_atr_from_ohlc(self, ohlc_data: pd.DataFrame, period: int = 14) -> float:
        """Calculate ATR using proper OHLC data"""
        if len(ohlc_data) < period + 1:
            return np.std(ohlc_data['close']) if len(ohlc_data) > 1 else 0
        
        high = ohlc_data['high']
        low = ohlc_data['low']
        close = ohlc_data['close']
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean().iloc[-1]
        
        return atr if not pd.isna(atr) else 0
    
    # [Include other necessary methods from the original implementation]
    def calculate_position_size(self, price: float) -> float:
        """Calculate position size based on available capital and risk management"""
        base_size = self.available_capital * self.position_size_pct / price

        # Adjust for current position exposure (both long and short)
        long_exposure = abs(self.position.long_position.quantity * price)
        short_exposure = abs(self.position.short_position.quantity * price)
        current_exposure = long_exposure + short_exposure
        max_exposure = self.initial_capital * self.max_position_ratio

        if current_exposure >= max_exposure:
            return 0

        remaining_exposure = max_exposure - current_exposure
        max_size_by_exposure = remaining_exposure / price

        return min(base_size, max_size_by_exposure)
    
    def should_rebalance_grid(self, current_price: float, current_time: pd.Timestamp = None) -> bool:
        """Check if grid needs rebalancing"""
        if not self.current_range or not self.grid_levels:
            return True

        # Time-based constraint: Don't rebalance too frequently
        if current_time and self.last_rebalance_time is not None:
            time_since_last_rebalance = current_time - self.last_rebalance_time
            min_rebalance_interval = pd.Timedelta(hours=2)  # Minimum 2 hours between rebalances
            if time_since_last_rebalance < min_rebalance_interval:
                return False

        lower_bound, upper_bound = self.current_range

        # Only rebalance if price breaks VERY SIGNIFICANTLY outside the range
        # Use an even larger buffer to let the grid work properly
        range_size = upper_bound - lower_bound
        buffer = range_size * 0.5  # 50% buffer outside the range

        if current_price < (lower_bound - buffer) or current_price > (upper_bound + buffer):
            return True

        return False
    
    def check_stop_loss(self, current_price: float) -> bool:
        """Check if stop loss should be triggered for hedged positions"""
        # Check stop loss for both long and short positions
        long_pos = self.position.long_position
        short_pos = self.position.short_position

        # Check long position stop loss
        if long_pos.quantity > 0:
            current_value = long_pos.quantity * current_price
            position_value = long_pos.quantity * long_pos.avg_price
            if position_value != 0:
                pnl_pct = (current_value - position_value) / abs(position_value)
                if pnl_pct < -self.stop_loss_pct:
                    return True

        # Check short position stop loss
        if short_pos.quantity > 0:
            current_value = -short_pos.quantity * current_price  # Short position value
            position_value = -short_pos.quantity * short_pos.avg_price
            if position_value != 0:
                pnl_pct = (current_value - position_value) / abs(position_value)
                if pnl_pct < -self.stop_loss_pct:
                    return True

        return False

    def print_kline_state(self, market_data):
        """Print detailed state for selected k-lines"""
        if not self.verbose_debug:
            return

        self.kline_counter += 1

        # Print state when:
        # 1. Orders are executed (filled_orders changed)
        # 2. Grid is rebalanced
        # 3. Every 50th k-line for regular updates
        # 4. First 10 k-lines for initial setup

        should_print = (
            self.kline_counter <= 10 or  # First 10 k-lines
            self.kline_counter % 50 == 0 or  # Every 50th k-line
            len(self.filled_orders) > getattr(self, '_last_filled_count', 0) or  # New fills
            hasattr(self, '_just_rebalanced')  # Just rebalanced
        )

        if should_print:
            print(f"\n{'='*80}")
            print(f"K-LINE #{self.kline_counter}: {market_data.timestamp} | Price: ${market_data.close:.2f}")
            print(f"{'='*80}")

            # Grid range
            if self.current_range:
                print(f"Grid Range: ${self.current_range[0]:.2f} - ${self.current_range[1]:.2f}")
            else:
                print("Grid Range: Not set")

            # Positions
            print(f"Positions:")
            print(f"  Long:  {self.position.long_position.quantity:.6f} @ avg ${self.position.long_position.avg_price:.2f}")
            print(f"  Short: {self.position.short_position.quantity:.6f} @ avg ${self.position.short_position.avg_price:.2f}")
            print(f"  Net:   {self.position.net_quantity:.6f}")

            # Open orders (show first 10 to avoid spam)
            print(f"Open Orders ({len(self.active_orders)}):")
            if self.active_orders:
                order_items = list(self.active_orders.items())[:10]  # Show first 10
                for order_id, order in order_items:
                    grid_side = getattr(order, 'grid_side', 'unknown')
                    print(f"  {order_id}: {grid_side} {order.order_type.value} {order.quantity:.6f} @ ${order.price:.2f}")
                if len(self.active_orders) > 10:
                    print(f"  ... and {len(self.active_orders) - 10} more orders")
            else:
                print("  None")

            # Recent filled orders (last 5)
            recent_fills = self.filled_orders[-5:] if len(self.filled_orders) >= 5 else self.filled_orders
            print(f"Recent Filled Orders (last {len(recent_fills)}):")
            if recent_fills:
                for i, order in enumerate(recent_fills, 1):
                    action_label = getattr(order, 'action_label', 'unknown')
                    print(f"  {i}. {action_label} {order.quantity:.6f} @ ${order.price:.2f}")
            else:
                print("  None")

            # Equity
            equity = self.equity_curve[-1] if self.equity_curve else self.initial_capital
            print(f"Equity: ${equity:.2f}")
            print(f"Total Filled Orders: {len(self.filled_orders)}")
            print(f"{'='*80}")

        # Update tracking variables
        self._last_filled_count = len(self.filled_orders)
        if hasattr(self, '_just_rebalanced'):
            delattr(self, '_just_rebalanced')

    def get_performance_metrics(self) -> Dict:
        """Calculate and return performance metrics with timestamps"""
        if len(self.equity_curve) < 1:
            return {}
        
        equity_series = pd.Series(self.equity_curve, index=self.timestamps if self.timestamps else range(len(self.equity_curve)))
        returns = equity_series.pct_change().dropna()
        
        total_return = (self.equity_curve[-1] - self.initial_capital) / self.initial_capital if self.equity_curve else 0
        win_rate = self.winning_trades / max(self.total_trades, 1)
        
        sharpe_ratio = 0
        max_drawdown = 0
        
        if len(returns) > 0:
            # Annualized Sharpe ratio (assuming hourly data)
            periods_per_year = 365 * 24  # hourly data
            sharpe_ratio = np.sqrt(periods_per_year) * returns.mean() / returns.std() if returns.std() > 0 else 0
            
            # Calculate max drawdown
            peak = equity_series.expanding().max()
            drawdown = (equity_series - peak) / peak
            max_drawdown = drawdown.min()
        
        # Calculate trading frequency
        if self.timestamps and len(self.timestamps) > 1:
            total_time = self.timestamps[-1] - self.timestamps[0]
            trades_per_day = self.total_trades / max(total_time.days, 1)
        else:
            trades_per_day = 0
        
        return {
            'total_return': total_return,
            'total_trades': self.total_trades,
            'win_rate': win_rate,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_fees': self.total_fees,
            'long_position': self.position.long_position.quantity,
            'short_position': self.position.short_position.quantity,
            'net_position': self.position.net_quantity,
            'realized_pnl': self.position.total_realized_pnl,
            'unrealized_pnl': self.position.total_unrealized_pnl,
            'active_orders': len(self.active_orders),
            'current_range': self.current_range,
            'trades_per_day': trades_per_day,
            'final_equity': self.equity_curve[-1] if self.equity_curve else self.initial_capital,
            'data_points': len(self.price_history),
            'date_range': f"{self.timestamps[0]} to {self.timestamps[-1]}" if self.timestamps else "N/A"
        }
    
    # [Additional methods as needed - cancel_all_orders, execute_order, etc.]
    def cancel_all_orders(self):
        """Cancel all pending orders"""
        for order in self.active_orders.values():
            order.status = OrderStatus.CANCELLED
        self.active_orders.clear()
        self.long_grid_orders.clear()
        self.short_grid_orders.clear()
    
    def execute_order(self, order: GridOrder, current_price: float, timestamp: pd.Timestamp = None):
        """Execute a grid order"""
        if order.status != OrderStatus.PENDING:
            return

        # Check if order should be triggered
        should_execute = (
            (order.order_type == OrderType.BUY and current_price <= order.price) or
            (order.order_type == OrderType.SELL and current_price >= order.price)
        )

        if not should_execute:
            return

        # Execute the order
        order.status = OrderStatus.FILLED
        if timestamp:
            order.timestamp = timestamp
        self.filled_orders.append(order)
        
        # Update position based on grid side
        if order.grid_side == "long":
            position = self.position.long_position
        else:  # short
            position = self.position.short_position

        if order.order_type == OrderType.BUY:
            if order.grid_side == "long":
                # OPEN_LONG - Buy orders that increase long position
                new_quantity = position.quantity + order.quantity
                if position.quantity != 0:
                    position.avg_price = (
                        (position.avg_price * position.quantity + order.price * order.quantity) / new_quantity
                    )
                else:
                    position.avg_price = order.price
                position.quantity = new_quantity
                order.action_label = "open_long"
            else:
                # CLOSE_SHORT - Buy orders that reduce short position (buy to cover)
                if position.quantity >= order.quantity:
                    # Calculate PnL for short position (profit when price goes down)
                    if position.avg_price > 0:
                        pnl = (position.avg_price - order.price) * order.quantity
                        position.realized_pnl += pnl

                        # Count as winning trade if profitable
                        if pnl > 0:
                            self.winning_trades += 1
                            self.logger.info(f"✅ Winning close_short: Bought {order.quantity:.4f} @ {order.price:.2f}, avg short {position.avg_price:.2f}, PnL: ${pnl:.2f}")
                        else:
                            self.logger.info(f"❌ Losing close_short: Bought {order.quantity:.4f} @ {order.price:.2f}, avg short {position.avg_price:.2f}, PnL: ${pnl:.2f}")

                    position.quantity -= order.quantity

                    # Update average price if position is reduced to zero
                    if position.quantity == 0:
                        position.avg_price = 0
                    order.action_label = "close_short"
                else:
                    self.logger.warning(f"Cannot close_short {order.quantity:.4f} units, only have {position.quantity:.4f}")
                    return
        else:  # SELL
            if order.grid_side == "long":
                # CLOSE_LONG - Sell orders that reduce long position
                if position.quantity >= order.quantity:
                    # Calculate PnL for long position
                    if position.avg_price > 0:
                        pnl = (order.price - position.avg_price) * order.quantity
                        position.realized_pnl += pnl

                        # Count as winning trade if profitable
                        if pnl > 0:
                            self.winning_trades += 1
                            self.logger.info(f"✅ Winning close_long: Sold {order.quantity:.4f} @ {order.price:.2f}, avg cost {position.avg_price:.2f}, PnL: ${pnl:.2f}")
                        else:
                            self.logger.info(f"❌ Losing close_long: Sold {order.quantity:.4f} @ {order.price:.2f}, avg cost {position.avg_price:.2f}, PnL: ${pnl:.2f}")

                    position.quantity -= order.quantity

                    # Update average price if position is reduced to zero
                    if position.quantity == 0:
                        position.avg_price = 0
                    order.action_label = "close_long"
                else:
                    self.logger.warning(f"Cannot close_long {order.quantity:.4f} units, only have {position.quantity:.4f}")
                    return
            else:
                # OPEN_SHORT - Sell orders that increase short position
                new_quantity = position.quantity + order.quantity
                if position.quantity != 0:
                    position.avg_price = (
                        (position.avg_price * position.quantity + order.price * order.quantity) / new_quantity
                    )
                else:
                    position.avg_price = order.price
                position.quantity = new_quantity
                order.action_label = "open_short"
        
        # Update capital
        trade_value = order.price * order.quantity
        fee = trade_value * 0.001
        self.total_fees += fee
        
        if order.order_type == OrderType.BUY:
            self.available_capital -= (trade_value + fee)
        else:
            self.available_capital += (trade_value - fee)
        
        # Remove from active orders
        if order.id in self.active_orders:
            del self.active_orders[order.id]
        
        self.total_trades += 1
        timestamp_str = f" at {order.timestamp}" if order.timestamp else ""
        action_str = f" ({order.action_label})" if order.action_label else ""
        self.logger.info(f"Executed {order.order_type.value} order: {order.quantity:.4f} @ {order.price:.2f}{action_str}{timestamp_str}")
    
    def get_status(self) -> Dict:
        """Get current system status"""
        return {
            'symbol': self.symbol,
            'current_range': self.current_range,
            'grid_levels_count': len(self.grid_levels),
            'active_orders': len(self.active_orders),
            'position': {
                'long_quantity': self.position.long_position.quantity,
                'long_avg_price': self.position.long_position.avg_price,
                'short_quantity': self.position.short_position.quantity,
                'short_avg_price': self.position.short_position.avg_price,
                'net_quantity': self.position.net_quantity,
                'total_unrealized_pnl': self.position.total_unrealized_pnl,
                'total_realized_pnl': self.position.total_realized_pnl
            },
            'available_capital': self.available_capital,
            'total_equity': self.equity_curve[-1] if self.equity_curve else self.initial_capital
        }
    
    # Placeholder methods that would need full implementation
    def generate_grid_levels(self, current_price: float) -> List[float]:
        """Generate grid levels within the current range"""
        if self.current_range[0] == 0 and self.current_range[1] == 0:
            return []

        lower_bound, upper_bound = self.current_range

        # Calculate grid spacing
        range_size = upper_bound - lower_bound
        grid_spacing = range_size / self.num_grid_levels

        # Generate grid levels
        levels = []
        for i in range(self.num_grid_levels + 1):
            level = lower_bound + i * grid_spacing
            if level != current_price:  # Don't place orders at current price
                levels.append(level)

        return levels
    
    def update_range_with_position_management(self, new_range, current_price, reason):
        """Update trading range with position management"""
        lower_bound, upper_bound = new_range

        # Cancel all existing orders
        self.cancel_all_orders()

        # Update the current range
        self.current_range = (lower_bound, upper_bound)

        # Generate new grid levels
        grid_levels = self.generate_grid_levels(current_price)
        self.grid_levels = grid_levels  # Store the grid levels

        # Initialize base positions if we don't have any (for first setup)
        if self.position.long_position.quantity == 0 and self.position.short_position.quantity == 0 and len(self.filled_orders) == 0:
            # Initialize both long and short base positions
            base_quantity = round(self.grid_size_usd / current_price, 6)

            # Long position
            self.position.long_position.quantity = base_quantity
            self.position.long_position.avg_price = current_price

            # Short position
            self.position.short_position.quantity = base_quantity
            self.position.short_position.avg_price = current_price

            # Adjust capital for both positions (long costs money, short provides money)
            self.available_capital -= base_quantity * current_price  # Long position cost
            self.available_capital += base_quantity * current_price  # Short position proceeds

            self.logger.info(f"Initialized hedged positions: Long {base_quantity:.6f} @ {current_price:.2f}, Short {base_quantity:.6f} @ {current_price:.2f}")

        # Place new orders at grid levels for BOTH long and short grids
        for level in grid_levels:
            # Calculate quantity based on dollar amount and price
            quantity = round(self.grid_size_usd / level, 6)  # Round to 6 decimal places

            if level < current_price:
                # LONG GRID: Place buy order below current price (accumulate long position)
                order_id = f"long_buy_{len(self.active_orders)}"
                order = GridOrder(
                    id=order_id,
                    price=level,
                    quantity=quantity,
                    order_type=OrderType.BUY,
                    status=OrderStatus.PENDING,
                    timestamp=pd.Timestamp.now(),
                    grid_level=0,
                    grid_side="long"
                )
                self.active_orders[order_id] = order

                # SHORT GRID: Place sell order below current price (accumulate short position)
                order_id = f"short_sell_{len(self.active_orders)}"
                order = GridOrder(
                    id=order_id,
                    price=level,
                    quantity=quantity,
                    order_type=OrderType.SELL,
                    status=OrderStatus.PENDING,
                    timestamp=pd.Timestamp.now(),
                    grid_level=0,
                    grid_side="short"
                )
                self.active_orders[order_id] = order

            elif level > current_price:
                # LONG GRID: Place sell order above current price (take profit on long)
                max_sellable = self.position.long_position.quantity
                sell_quantity = min(quantity, max_sellable)

                if sell_quantity > 0:
                    order_id = f"long_sell_{len(self.active_orders)}"
                    order = GridOrder(
                        id=order_id,
                        price=level,
                        quantity=sell_quantity,
                        order_type=OrderType.SELL,
                        status=OrderStatus.PENDING,
                        timestamp=pd.Timestamp.now(),
                        grid_level=0,
                        grid_side="long"
                    )
                    self.active_orders[order_id] = order

                # SHORT GRID: Place buy order above current price (take profit on short)
                max_coverable = self.position.short_position.quantity
                cover_quantity = min(quantity, max_coverable)

                if cover_quantity > 0:
                    order_id = f"short_buy_{len(self.active_orders)}"
                    order = GridOrder(
                        id=order_id,
                        price=level,
                        quantity=cover_quantity,
                        order_type=OrderType.BUY,
                        status=OrderStatus.PENDING,
                        timestamp=pd.Timestamp.now(),
                        grid_level=0,
                        grid_side="short"
                    )
                    self.active_orders[order_id] = order

# ==============================================================================
# USAGE EXAMPLES AND TESTING
# ==============================================================================

def test_yahoo_finance_data():
    """Test with Yahoo Finance stock data"""
    print("=== Testing with Yahoo Finance Data (AAPL) ===")
    
    grid = RealDataDynamicHedgedGrid(
        symbol="AAPL",
        data_source=DataSource.YAHOO,
        initial_capital=10000,
        grid_density=10,
        position_size_pct=0.02,
        period="6mo",  # 6 months of data
        interval="1h"   # 1-hour intervals
    )
    
    performance = grid.load_and_backtest()
    print_performance_summary(performance, grid)

    return grid

def test_crypto_data():
    """Test with cryptocurrency data"""
    print("=== Testing with Cryptocurrency Data (BTC/USDT) ===")
    
    grid = RealDataDynamicHedgedGrid(
        symbol="BTC/USDT",
        data_source=DataSource.BINANCE,
        initial_capital=10000,
        grid_density=15,
        position_size_pct=0.03,
        timeframe="1h",
        limit=2000  # Last 2000 hours
    )
    
    performance = grid.load_and_backtest()
    print_performance_summary(performance, grid)

    return grid

def test_csv_data():
    """Test with CSV file data"""
    print("=== Testing with CSV Data ===")
    
    # Create sample CSV data first
    create_sample_csv()
    
    grid = RealDataDynamicHedgedGrid(
        symbol="SAMPLE",
        data_source=DataSource.CSV_FILE,
        initial_capital=10000,
        grid_density=12,
        file_path="sample_market_data.csv",
        timestamp_col="timestamp",
        price_col="close"
    )
    
    performance = grid.load_and_backtest()
    print_performance_summary(performance, grid)

    return grid

def create_sample_csv():
    """Create a sample CSV file for testing"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='H')
    np.random.seed(42)
    
    # Generate realistic price data
    initial_price = 100
    prices = [initial_price]
    
    for i in range(len(dates) - 1):
        change = np.random.normal(0, 0.02)
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1))
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000, 10000, len(dates))
    })
    
    df.to_csv('sample_market_data.csv', index=False)
    print("Created sample_market_data.csv")

def print_performance_summary(performance: Dict, grid: RealDataDynamicHedgedGrid = None):
    """Print formatted performance summary"""
    print("\n" + "="*50)
    print("PERFORMANCE SUMMARY")
    print("="*50)

    for key, value in performance.items():
        if isinstance(value, float):
            if 'return' in key or 'rate' in key:
                print(f"{key:20s}: {value:8.2%}")
            elif 'ratio' in key:
                print(f"{key:20s}: {value:8.2f}")
            elif 'pnl' in key or 'fees' in key or 'equity' in key:
                print(f"{key:20s}: ${value:,.2f}")
            elif 'drawdown' in key:
                print(f"{key:20s}: {value:8.2%}")
            else:
                print(f"{key:20s}: {value:8.4f}")
        else:
            print(f"{key:20s}: {value}")

    print("="*50)

    # Print filled orders with k-line timestamps
    if grid and grid.filled_orders:
        print("\nFILLED ORDERS WITH K-LINE TIMESTAMPS:")
        print("="*80)
        for i, order in enumerate(grid.filled_orders, 1):
            timestamp_str = order.timestamp.strftime('%Y-%m-%d %H:%M:%S') if order.timestamp else "N/A"
            action_label = getattr(order, 'action_label', 'unknown')
            print(f"{i:3d}. {action_label:11s} {order.quantity:.6f} @ ${order.price:8.2f} | {timestamp_str}")
        print("="*80)

def plot_real_data_results(grid: RealDataDynamicHedgedGrid):
    """Plot results for real data backtests"""
    if not grid.timestamps:
        print("No timestamp data available for plotting")
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Price evolution
    ax1 = axes[0, 0]
    ax1.plot(grid.timestamps, grid.price_history, label='Price', alpha=0.7, linewidth=1)
    if grid.current_range[0] > 0:
        ax1.axhline(y=grid.current_range[0], color='r', linestyle='--', alpha=0.7, label='Current Range')
        ax1.axhline(y=grid.current_range[1], color='r', linestyle='--', alpha=0.7)
    ax1.set_title(f'Price Evolution - {grid.symbol}')
    ax1.set_xlabel('Time')
    ax1.set_ylabel('Price')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Equity curve
    ax2 = axes[0, 1]
    equity_timestamps = grid.timestamps[:len(grid.equity_curve)]
    ax2.plot(equity_timestamps, grid.equity_curve, label='Total Equity', color='blue')
    ax2.axhline(y=grid.initial_capital, color='black', linestyle='--', alpha=0.5, label='Initial Capital')
    ax2.set_title('Equity Curve')
    ax2.set_xlabel('Time')
    ax2.set_ylabel('Equity ($)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Trading activity
    ax3 = axes[1, 0]
    if grid.filled_orders:
        trade_times = [order.timestamp for order in grid.filled_orders]
        trade_prices = [order.price for order in grid.filled_orders]
        buy_times = [order.timestamp for order in grid.filled_orders if order.order_type == OrderType.BUY]
        buy_prices = [order.price for order in grid.filled_orders if order.order_type == OrderType.BUY]
        sell_times = [order.timestamp for order in grid.filled_orders if order.order_type == OrderType.SELL]
        sell_prices = [order.price for order in grid.filled_orders if order.order_type == OrderType.SELL]
        
        ax3.scatter(buy_times, buy_prices, color='green', alpha=0.6, s=20, label='Buy Orders')
        ax3.scatter(sell_times, sell_prices, color='red', alpha=0.6, s=20, label='Sell Orders')
        ax3.plot(grid.timestamps, grid.price_history, alpha=0.3, color='gray', linewidth=0.5)
        ax3.set_title('Trading Activity')
        ax3.set_xlabel('Time')
        ax3.set_ylabel('Price')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    
    # Performance metrics over time
    ax4 = axes[1, 1]
    if len(grid.equity_curve) > 1:
        equity_series = pd.Series(grid.equity_curve, index=equity_timestamps)
        returns = equity_series.pct_change().dropna()
        rolling_sharpe = returns.rolling(window=24*7).mean() / returns.rolling(window=24*7).std() * np.sqrt(24*365)  # Weekly rolling Sharpe
        
        ax4.plot(rolling_sharpe.index, rolling_sharpe.values, label='Rolling Sharpe (Weekly)', color='purple')
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax4.set_title('Rolling Performance Metrics')
        ax4.set_xlabel('Time')
        ax4.set_ylabel('Sharpe Ratio')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# ==============================================================================
# MAIN EXECUTION EXAMPLES
# ==============================================================================

if __name__ == "__main__":
    print("Dynamic Hedged Grid Trading System - Real Data Integration")
    print("="*60)
    
    try:
        # Test different data sources
        
        # 1. Yahoo Finance (Stocks)
        # if YFINANCE_AVAILABLE:
        #     print("\n1. Testing Yahoo Finance Data...")
        #     stock_grid = test_yahoo_finance_data()
        #     plot_real_data_results(stock_grid)
        
        # 2. Cryptocurrency
        if CCXT_AVAILABLE:
            print("\n2. Testing Cryptocurrency Data...")
            crypto_grid = test_crypto_data()
            plot_real_data_results(crypto_grid)
        
        # 3. CSV File
        # print("\n3. Testing CSV File Data...")
        # csv_grid = test_csv_data()
        # plot_real_data_results(csv_grid)
        
        print("\nAll tests completed!")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

    # Example of how to start live trading (commented out for safety)
    """
    # LIVE TRADING EXAMPLE (USE WITH CAUTION!)
    live_grid = RealDataDynamicHedgedGrid(
        symbol="BTC/USDT",
        data_source=DataSource.BINANCE,
        initial_capital=1000,  # Start small!
        grid_density=10,
        position_size_pct=0.01,  # Very small position sizes
        timeframe="1m",
        limit=100
    )
    
    # This would start live trading - DO NOT USE WITH REAL MONEY WITHOUT THOROUGH TESTING
    # live_grid.start_live_trading(update_interval=60)
    """